<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire API 精简测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .account-selector {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .account-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .account-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .account-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }
        .account-card.active {
            border-color: #28a745;
            background: #f8fff9;
        }
        .account-card.authenticated {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover { opacity: 0.8; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            display: none;
        }
        .result-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result-pending { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .result-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        .account-info {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        .backend-user-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔧 GoMyHire API 精简测试工具</h1>
            <p>账号认证测试 + 手动订单创建测试</p>
        </div>

        <!-- 账号选择器 -->
        <div class="account-selector">
            <h3>📧 账号选择与认证</h3>
            <div class="account-grid" id="accountGrid">
                <!-- 账号卡片将通过JavaScript动态生成 -->
            </div>
            <div class="backend-user-info" id="backendUserInfo" style="display: none;">
                <!-- 后台用户信息将在这里显示 -->
            </div>
        </div>

        <!-- 认证测试 -->
        <div class="test-section">
            <h3>🔐 账号认证测试</h3>
            <button type="button" class="btn btn-primary" onclick="testAuthentication()">🔐 测试认证</button>
            <div id="authTestResults" class="test-result"></div>
        </div>

        <!-- 手动订单测试 -->
        <div class="test-section">
            <h3>📝 手动订单测试</h3>
            <div class="grid-2">
                <div class="form-group">
                    <label for="testOrderType">订单类型:</label>
                    <select id="testOrderType" class="form-control">
                        <option value="pickup">接机服务</option>
                        <option value="dropoff">送机服务</option>
                        <option value="charter">包车服务</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="testPassengerCount">乘客人数:</label>
                    <input type="number" id="testPassengerCount" class="form-control" value="2" min="1" max="50">
                </div>
            </div>
            <div class="grid-2">
                <div class="form-group">
                    <label for="testPickup">接机地点:</label>
                    <input type="text" id="testPickup" class="form-control" value="KLIA Airport">
                </div>
                <div class="form-group">
                    <label for="testDestination">目的地:</label>
                    <input type="text" id="testDestination" class="form-control" value="KLCC">
                </div>
            </div>
            <div class="grid-3">
                <div class="form-group">
                    <label for="testCustomerName">客户姓名:</label>
                    <input type="text" id="testCustomerName" class="form-control" value="张三">
                </div>
                <div class="form-group">
                    <label for="testCustomerPhone">客户电话:</label>
                    <input type="text" id="testCustomerPhone" class="form-control" value="+60123456789">
                </div>
                <div class="form-group">
                    <label for="testCustomerEmail">客户邮箱:</label>
                    <input type="text" id="testCustomerEmail" class="form-control" value="<EMAIL>">
                </div>
            </div>
            <div class="grid-2">
                <div class="form-group">
                    <label for="testPickupDate">接机日期 (DD-MM-YYYY):</label>
                    <input type="text" id="testPickupDate" class="form-control" value="25-12-2024">
                </div>
                <div class="form-group">
                    <label for="testPickupTime">接机时间:</label>
                    <input type="text" id="testPickupTime" class="form-control" value="10:00">
                </div>
            </div>
            <div class="grid-3">
                <button type="button" class="btn btn-success" onclick="createManualTestOrder()">🚗 创建测试订单</button>
                <button type="button" class="btn btn-info" onclick="previewOrderData()">👁️ 预览订单数据</button>
            </div>
            <div id="manualTestResults" class="test-result"></div>
        </div>
    </div>

    <script>
        /**
         * @file GoMyHire API 精简测试工具
         * @description 账号认证测试 + 手动订单创建测试
         */

        // ========== 全局配置和变量 ==========

        /**
         * @constant {Object} API_CONFIG - API配置
         */
        const API_CONFIG = {
            BASE_URL: 'https://gomyhire.com.my/api',
            ENDPOINTS: {
                login: '/login',
                create_order: '/create_order'
            }
        };

        /**
         * @constant {Array} TEST_ACCOUNTS - 预设测试账号
         */
        const TEST_ACCOUNTS = [
            {
                id: 'general',
                email: '<EMAIL>',
                password: 'Gomyhire@123456',
                name: 'General Admin',
                isDefault: true,
                backendUserId: null
            },
            {
                id: 'jcy',
                email: '<EMAIL>',
                password: 'Yap123',
                name: 'Jcy',
                isDefault: false,
                backendUserId: 310
            },
            {
                id: 'skymirror',
                email: '<EMAIL>',
                password: 'Sky@114788',
                name: 'Sky Mirror World',
                isDefault: false,
                backendUserId: 37
            }
        ];

        /**
         * @constant {Object} STATIC_DATA - 基础静态数据
         */
        const STATIC_DATA = {
            subCategories: [
                { id: 2, name: 'Pickup', type: 'pickup' },
                { id: 3, name: 'Dropoff', type: 'dropoff' },
                { id: 4, name: 'Charter', type: 'charter' }
            ],
            carTypes: [
                { id: 5, type: '5 Seater', seat_number: 3 },
                { id: 15, type: '7 Seater MPV', seat_number: 5 },
                { id: 20, type: '10 Seater MPV / Van', seat_number: 7 }
            ]
        };

        // 全局状态变量
        let currentAccount = null;
        let authTokens = {}; // 存储每个账号的token
        let apiData = {
            subCategories: [],
            carTypes: []
        };

        // ========== 初始化函数 ==========

        /**
         * @function initializeApp - 初始化应用
         * @description 页面加载时初始化所有组件
         */
        function initializeApp() {
            console.log('🚀 初始化GoMyHire API精简测试工具...');

            // 渲染账号选择器
            renderAccountSelector();

            // 加载静态数据
            loadStaticData();

            // 设置默认账号
            const defaultAccount = TEST_ACCOUNTS.find(acc => acc.isDefault);
            if (defaultAccount) {
                selectAccount(defaultAccount.id);
            }

            console.log('✅ 应用初始化完成');
        }

        // ========== 账号管理函数 ==========

        /**
         * @function renderAccountSelector - 渲染账号选择器
         * @description 动态生成账号卡片界面
         */
        function renderAccountSelector() {
            const accountGrid = document.getElementById('accountGrid');
            accountGrid.innerHTML = '';

            TEST_ACCOUNTS.forEach(account => {
                const accountCard = document.createElement('div');
                accountCard.className = 'account-card';
                accountCard.id = `account-${account.id}`;
                accountCard.onclick = () => selectAccount(account.id);

                const isAuthenticated = authTokens[account.id] ? 'authenticated' : '';
                if (isAuthenticated) {
                    accountCard.classList.add('authenticated');
                }

                accountCard.innerHTML = `
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span class="status-indicator ${isAuthenticated ? 'status-success' : 'status-pending'}"></span>
                        <strong>${account.name}</strong>
                        ${account.isDefault ? '<span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 8px;">默认</span>' : ''}
                    </div>
                    <div class="account-info">
                        📧 ${account.email}<br>
                        👤 后台用户ID: ${account.backendUserId || '自动选择'}
                    </div>
                `;

                accountGrid.appendChild(accountCard);
            });
        }

        /**
         * @function selectAccount - 选择账号
         * @param {string} accountId - 账号ID
         * @description 切换当前活跃账号并更新界面状态
         */
        function selectAccount(accountId) {
            // 移除之前的active状态
            document.querySelectorAll('.account-card').forEach(card => {
                card.classList.remove('active');
            });

            // 设置新的active状态
            const selectedCard = document.getElementById(`account-${accountId}`);
            if (selectedCard) {
                selectedCard.classList.add('active');
            }

            // 更新当前账号
            currentAccount = TEST_ACCOUNTS.find(acc => acc.id === accountId);

            // 更新后台用户信息显示
            updateBackendUserInfo();

            console.log(`🔄 切换到账号: ${currentAccount.name} (${currentAccount.email})`);
        }

        /**
         * @function updateBackendUserInfo - 更新后台用户信息显示
         * @description 显示当前账号对应的后台用户信息
         */
        function updateBackendUserInfo() {
            const backendUserInfo = document.getElementById('backendUserInfo');

            if (!currentAccount) {
                backendUserInfo.style.display = 'none';
                return;
            }

            let userInfo = '';
            if (currentAccount.backendUserId) {
                userInfo = `固定映射: ID ${currentAccount.backendUserId}`;
            } else {
                userInfo = '使用默认逻辑: 自动选择';
            }

            backendUserInfo.innerHTML = `
                <strong>当前账号后台用户映射:</strong><br>
                ${userInfo}
            `;
            backendUserInfo.style.display = 'block';
        }

        // ========== 数据管理函数 ==========

        /**
         * @function loadStaticData - 加载静态数据
         * @description 从STATIC_DATA常量加载静态映射数据
         */
        function loadStaticData() {
            console.log('📦 加载静态映射数据...');

            // 复制静态数据到apiData
            Object.keys(STATIC_DATA).forEach(key => {
                apiData[key] = [...STATIC_DATA[key]];
            });

            console.log('✅ 静态数据加载完成');
        }

        // ========== API调用函数 ==========

        /**
         * @function makeAPICall - 执行API调用
         * @param {string} endpoint - API端点
         * @param {Object} data - 请求数据
         * @param {string} method - HTTP方法
         * @param {string} accountId - 账号ID
         * @returns {Promise<Object>} API响应
         * @description 统一的API调用函数，包含错误处理
         */
        async function makeAPICall(endpoint, data = {}, method = 'POST', accountId = null) {
            const account = accountId ? TEST_ACCOUNTS.find(acc => acc.id === accountId) : currentAccount;
            if (!account) {
                throw new Error('未选择有效账号');
            }

            const url = `${API_CONFIG.BASE_URL}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };

            // 添加认证token（如果有）
            if (authTokens[account.id]) {
                headers['Authorization'] = `Bearer ${authTokens[account.id]}`;
            }

            const requestConfig = {
                method: method,
                headers: headers
            };

            if (method !== 'GET') {
                requestConfig.body = JSON.stringify(data);
            }

            try {
                console.log(`🌐 API调用: ${method} ${url}`, data);

                const response = await fetch(url, requestConfig);
                const responseData = await response.json();

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseData.message || '未知错误'}`);
                }

                console.log(`✅ API调用成功: ${endpoint}`, responseData);
                return responseData;

            } catch (err) {
                console.error(`❌ API调用失败: ${endpoint}`, err);
                throw err;
            }
        }

        // ========== 认证函数 ==========

        /**
         * @function authenticateAccount - 认证账号
         * @param {string} accountId - 账号ID
         * @returns {Promise<Object>} 认证结果
         * @description 对指定账号进行认证并获取token
         */
        async function authenticateAccount(accountId) {
            const account = TEST_ACCOUNTS.find(acc => acc.id === accountId);
            if (!account) {
                throw new Error(`账号 ${accountId} 不存在`);
            }

            try {
                const loginData = {
                    email: account.email,
                    password: account.password
                };

                console.log(`🔐 开始认证账号: ${account.email}`);
                const response = await makeAPICall(API_CONFIG.ENDPOINTS.login, loginData, 'POST', accountId);

                console.log(`📥 API响应:`, response);

                // 查找token字段
                const possibleTokenFields = ['access_token', 'token', 'accessToken'];
                let foundToken = null;
                let tokenField = null;

                for (const field of possibleTokenFields) {
                    if (response && response[field]) {
                        foundToken = response[field];
                        tokenField = field;
                        break;
                    }
                }

                // 检查嵌套对象
                if (!foundToken && response?.data) {
                    for (const field of possibleTokenFields) {
                        if (response.data[field]) {
                            foundToken = response.data[field];
                            tokenField = `data.${field}`;
                            break;
                        }
                    }
                }

                if (foundToken) {
                    authTokens[accountId] = foundToken;
                    console.log(`✅ 账号 ${account.email} 认证成功`);

                    // 更新界面状态
                    renderAccountSelector();

                    return {
                        success: true,
                        token: foundToken,
                        tokenField: tokenField,
                        account: account,
                        fullResponse: response
                    };
                } else {
                    throw new Error(`认证响应中未找到有效的token字段`);
                }

            } catch (error) {
                console.error(`❌ 账号 ${account.email} 认证失败:`, error);
                throw error;
            }
        }

        /**
         * @function testAuthentication - 测试认证功能
         * @description 测试当前账号的认证功能
         */
        async function testAuthentication() {
            const resultDiv = document.getElementById('authTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在测试认证功能...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            try {
                const result = await authenticateAccount(currentAccount.id);

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>✅ 认证测试成功</h4>
                    <p><strong>账号:</strong> ${result.account.email}</p>
                    <p><strong>Token:</strong> ${result.token.substring(0, 20)}...</p>
                    <p><strong>Token字段:</strong> ${result.tokenField || 'access_token'}</p>
                    <p><strong>后台用户ID:</strong> ${result.account.backendUserId || '自动选择'}</p>
                    <details>
                        <summary>查看完整响应</summary>
                        <pre>${JSON.stringify(result.fullResponse, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 认证测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>账号:</strong> ${currentAccount.email}</p>
                `;
            }
        }

        // ========== 订单创建函数 ==========

        /**
         * @function createManualTestOrder - 创建手动测试订单
         * @description 根据表单输入创建测试订单
         */
        async function createManualTestOrder() {
            const resultDiv = document.getElementById('manualTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在创建测试订单...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            if (!authTokens[currentAccount.id]) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先进行账号认证';
                return;
            }

            try {
                // 获取表单数据
                const orderType = document.getElementById('testOrderType').value;
                const passengerCount = parseInt(document.getElementById('testPassengerCount').value);
                const pickup = document.getElementById('testPickup').value;
                const destination = document.getElementById('testDestination').value;
                const customerName = document.getElementById('testCustomerName').value;
                const customerPhone = document.getElementById('testCustomerPhone').value;
                const customerEmail = document.getElementById('testCustomerEmail').value;
                const pickupDate = document.getElementById('testPickupDate').value;
                const pickupTime = document.getElementById('testPickupTime').value;

                // 智能选择逻辑
                const subCategory = apiData.subCategories.find(sc =>
                    sc.name.toLowerCase().includes(orderType) ||
                    (sc.id === 2 && orderType === 'pickup') ||
                    (sc.id === 3 && orderType === 'dropoff') ||
                    (sc.id === 4 && orderType === 'charter')
                ) || apiData.subCategories[0];

                const carType = apiData.carTypes.find(ct => {
                    const seatNumber = parseInt(ct.type.match(/(\d+)/)?.[1] || '0');
                    return seatNumber >= passengerCount;
                }) || apiData.carTypes.find(ct => ct.id === 5);

                // 获取后台用户ID
                const backendUserId = currentAccount.backendUserId || 1;

                // 构建API订单数据
                const apiOrderData = {
                    pickup: pickup,
                    destination: destination,
                    date: pickupDate,
                    time: pickupTime,
                    passenger_number: passengerCount,
                    car_type_id: carType?.id || 5,
                    sub_category_id: subCategory?.id || 2,
                    languages_id_array: {"0": "2", "1": "4"}, // 对象格式：英语+中文
                    incharge_by_backend_user_id: backendUserId,
                    customer_name: customerName,
                    customer_contact: customerPhone,
                    customer_email: customerEmail,
                    remarks: `手动测试订单 - ${new Date().toLocaleString()}`,
                    // OTA相关字段
                    ota_reference_number: `TEST${Date.now()}`,
                    ota: 'manual-test',
                    ota_price: '100.00',
                    // 其他必需字段
                    luggage_number: 2,
                    driving_region_id: 1
                };

                const response = await makeAPICall(API_CONFIG.ENDPOINTS.create_order, apiOrderData);

                // 检查API响应状态
                const isSuccess = response.status === true || response.status === 'true';
                const orderIdFound = response.order_id || response.id;

                if (isSuccess && orderIdFound) {
                    resultDiv.className = 'test-result result-success';
                    resultDiv.innerHTML = `
                        <h4>✅ 测试订单创建成功</h4>
                        <p><strong>订单ID:</strong> ${orderIdFound}</p>
                        <p><strong>API状态:</strong> <span style="color: #28a745;">成功 (${response.status})</span></p>
                        <p><strong>服务详情:</strong> ${pickup} → ${destination}</p>
                        <p><strong>日期时间:</strong> ${pickupDate} ${pickupTime}</p>
                        <p><strong>客户:</strong> ${customerName} (${customerPhone})</p>
                        <p><strong>乘客人数:</strong> ${passengerCount}</p>
                        <p><strong>OTA参考号:</strong> ${apiOrderData.ota_reference_number}</p>

                        <details>
                            <summary>查看API请求数据</summary>
                            <pre>${JSON.stringify(apiOrderData, null, 2)}</pre>
                        </details>
                        <details>
                            <summary>查看完整API响应</summary>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    resultDiv.className = 'test-result result-warning';
                    resultDiv.innerHTML = `
                        <h4>⚠️ 订单创建状态异常</h4>
                        <p><strong>API状态:</strong> <span style="color: #dc3545;">${response.status || '未知'}</span></p>
                        <p><strong>API消息:</strong> ${response.message || '无消息'}</p>
                        <p><strong>订单ID:</strong> ${orderIdFound || '未返回'}</p>

                        <details>
                            <summary>查看API请求数据</summary>
                            <pre>${JSON.stringify(apiOrderData, null, 2)}</pre>
                        </details>
                        <details>
                            <summary>查看完整API响应</summary>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        </details>
                    `;
                }

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 订单创建失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function previewOrderData - 预览订单数据
         * @description 预览将要发送的API订单数据
         */
        function previewOrderData() {
            const resultDiv = document.getElementById('manualTestResults');
            resultDiv.style.display = 'block';

            try {
                // 获取表单数据
                const orderType = document.getElementById('testOrderType').value;
                const passengerCount = parseInt(document.getElementById('testPassengerCount').value);
                const pickup = document.getElementById('testPickup').value;
                const destination = document.getElementById('testDestination').value;
                const customerName = document.getElementById('testCustomerName').value;
                const customerPhone = document.getElementById('testCustomerPhone').value;
                const customerEmail = document.getElementById('testCustomerEmail').value;
                const pickupDate = document.getElementById('testPickupDate').value;
                const pickupTime = document.getElementById('testPickupTime').value;

                // 智能选择逻辑
                const subCategory = apiData.subCategories.find(sc =>
                    sc.name.toLowerCase().includes(orderType) ||
                    (sc.id === 2 && orderType === 'pickup') ||
                    (sc.id === 3 && orderType === 'dropoff') ||
                    (sc.id === 4 && orderType === 'charter')
                ) || apiData.subCategories[0];

                const carType = apiData.carTypes.find(ct => {
                    const seatNumber = parseInt(ct.type.match(/(\d+)/)?.[1] || '0');
                    return seatNumber >= passengerCount;
                }) || apiData.carTypes.find(ct => ct.id === 5);

                const backendUserId = currentAccount.backendUserId || 1;

                // 构建API订单数据
                const apiOrderData = {
                    pickup: pickup,
                    destination: destination,
                    date: pickupDate,
                    time: pickupTime,
                    passenger_number: passengerCount,
                    car_type_id: carType?.id || 5,
                    sub_category_id: subCategory?.id || 2,
                    languages_id_array: {"0": "2", "1": "4"},
                    incharge_by_backend_user_id: backendUserId,
                    customer_name: customerName,
                    customer_contact: customerPhone,
                    customer_email: customerEmail,
                    remarks: `预览订单数据 - ${new Date().toLocaleString()}`,
                    ota_reference_number: `PREVIEW${Date.now()}`,
                    ota: 'preview-test',
                    ota_price: '100.00',
                    luggage_number: 2,
                    driving_region_id: 1
                };

                // 验证字段完整性
                const missingFields = [];
                if (!pickup) missingFields.push('接机地点');
                if (!destination) missingFields.push('目的地');
                if (!customerName) missingFields.push('客户姓名');
                if (!customerPhone) missingFields.push('客户电话');
                if (!customerEmail) missingFields.push('客户邮箱');
                if (!pickupDate) missingFields.push('接机日期');
                if (!pickupTime) missingFields.push('接机时间');

                // 验证日期格式
                const datePattern = /^\d{2}-\d{2}-\d{4}$/;
                const dateValid = datePattern.test(pickupDate);

                resultDiv.className = 'test-result result-warning';
                resultDiv.innerHTML = `
                    <h4>👁️ 订单数据预览 (API格式)</h4>

                    ${missingFields.length > 0 ? `
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin: 10px 0;">
                            <strong>⚠️ 缺少必填字段:</strong> ${missingFields.join(', ')}
                        </div>
                    ` : ''}

                    ${!dateValid ? `
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 10px; margin: 10px 0;">
                            <strong>❌ 日期格式错误:</strong> 请使用DD-MM-YYYY格式（如：25-12-2024）
                        </div>
                    ` : ''}

                    <p><strong>服务详情:</strong> ${pickup} → ${destination}</p>
                    <p><strong>日期时间:</strong> ${pickupDate} ${pickupTime}</p>
                    <p><strong>客户:</strong> ${customerName} (${customerPhone})</p>
                    <p><strong>乘客人数:</strong> ${passengerCount}</p>
                    <p><strong>车型:</strong> ${carType?.type || '未知'} (ID: ${carType?.id || 5})</p>
                    <p><strong>子分类:</strong> ${subCategory?.name || '未知'} (ID: ${subCategory?.id || 2})</p>
                    <p><strong>OTA参考号:</strong> ${apiOrderData.ota_reference_number}</p>

                    <details>
                        <summary>查看完整API请求数据</summary>
                        <pre>${JSON.stringify(apiOrderData, null, 2)}</pre>
                    </details>

                    <div style="margin-top: 15px;">
                        ${missingFields.length === 0 && dateValid ?
                            '<p style="color: #28a745;"><strong>✅ 数据验证通过，可以创建订单</strong></p>' :
                            '<p style="color: #dc3545;"><strong>❌ 请修正上述问题后再创建订单</strong></p>'
                        }
                    </div>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 订单数据预览失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        // 页面加载时初始化
        window.addEventListener('DOMContentLoaded', initializeApp);

    </script>
</body>
</html>
